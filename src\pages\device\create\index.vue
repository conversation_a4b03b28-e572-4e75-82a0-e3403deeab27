<script setup lang="ts">
import type { UploadFile } from 'element-plus'
import { ElMessage, ElUpload } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { $file, $get, $post } from '@/utils/api'

const { t } = useI18n()

// 格式化时间
const formatTime = (timestamp: number | string) => {
  if (!timestamp)
    return '-'
  const date = new Date(timestamp)

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

const modelList = ref([] as { label: string; value?: string }[])

const typeList = ref([
  {
    label: 'AP',
    value: '0',
  },
  {
    label: 'AC',
    value: '1',
  },
])

const filter = reactive({
  dType: '0',
  model: '',
})

// 获取AP型号列表
const getApModelList = () => {
  $get('/v1/apModel', {}).then(res => {
    if (res.msg === 'success') {
      const models = res.result || []

      modelList.value = [
        { label: '全部', value: '' },
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]
    }
  }).catch(err => {
    console.error('获取AP型号列表失败:', err)
    modelList.value = [{ label: '全部', value: '' }]
  })
}

// 获取AC型号列表
const getAcModelList = () => {
  $get('/v1/acModel', {}).then(res => {
    if (res.msg === 'success') {
      const models = res.result || []

      modelList.value = [
        { label: '全部', value: '' },
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]
    }
  }).catch(err => {
    console.error('获取AC型号列表失败:', err)
    modelList.value = [{ label: '全部', value: '' }]
  })
}

// 设备数据接口
interface Device {
  id: string
  model: string
  serialNumber: string
  ipAddress: string
  macAddress: string
  discoveryTime: string
  online: 'online' | 'offline'
  bindTime?: number | string
}

// 响应式数据
const loading = ref(false)
const isAddDrawerOpen = ref(false)
const currentTab = ref('manual')

// 分页状态 - 参考 device/ap 实现
const itemsPerPage = ref(10)
const page = ref(1)

// 排序相关变量
const sortBy = ref<{ key: string; order?: 'asc' | 'desc' | boolean }[]>([])

// 表头配置
const headers = [
  { title: '型号', key: 'model', sortable: true },
  { title: '序列号/SN', key: 'sn', sortable: true },
  { title: 'IP', key: 'ip', sortable: true },
  { title: 'MAC', key: 'mac', sortable: true },
  { title: '添加时间', key: 'bindTime', sortable: true },
  { title: '在线状态', key: 'online', sortable: false },
]

// 模拟设备数据
const deviceData = ref({
  total: 25,
  devices: [],
})

// 修改devices计算属性，支持本地排序 - 参考 device/ap 实现
const devices = computed((): Device[] => {
  let list = deviceData.value.devices

  // 本地排序
  if (sortBy.value.length > 0) {
    const { key, order } = sortBy.value[0]

    list = [...list].sort((a, b) => {
      const aValue = a[key as keyof Device]
      const bValue = b[key as keyof Device]
      if (aValue < bValue)
        return order === 'asc' ? -1 : 1
      if (aValue > bValue)
        return order === 'asc' ? 1 : -1

      return 0
    })
  }

  return list
})

// 计算总数
const totalDevices = computed(() => deviceData.value.devices.length || 0)

// 导入相关数据
const importHeaders = [
  { title: 'MAC', key: 'mac' },
  { title: '设备型号', key: 'model' },
  { title: '设备名称', key: 'name' },
  { title: '状态', key: 'errorCode' },
]

// 导入结果分页
const importItemsPerPage = ref(10)
const importPage = ref(1)

const importResults = ref([])

// 排序事件 - 参考 device/ap 实现
const sortchange = (val: any) => {
  sortBy.value = val
}

// 打开添加设备抽屉
const openAddDrawer = () => {
  isAddDrawerOpen.value = true
}

// 关闭添加设备抽屉
const closeAddDrawer = () => {
  isAddDrawerOpen.value = false
}

const dType = ref('0')

// 计算属性：是否显示型号选择器
const shouldShowModelList = computed(() => {
  return filter.dType !== '' // 当dType不为空时显示modelList
})

// 监听dType变化，动态加载对应的型号列表
watch(() => filter.dType, newDType => {
  // 重置型号选择
  filter.model = ''

  if (newDType === '0') {
    // 选择AP时，获取AP型号列表
    getApModelList()
  }
  else if (newDType === '1') {
    // 选择AC时，获取AC型号列表
    getAcModelList()
  }
  else {
    // 选择全部时，清空型号列表
    modelList.value = []
  }
}, { immediate: true })

// 搜索设备列表
const searchDevices = () => {
  loading.value = true

  const params: any = {
    page: page.value,
    size: itemsPerPage.value,
  }

  params.dType = 0

  // 只有当dType不为空时才添加到参数中
  if (filter.dType !== '')
    params.dType = filter.dType

  // 只有当model不为空时才添加到参数中
  if (filter.model !== '')
    params.model = filter.model

  $get('/v1/device/list', params).then(res => {
    console.log('设备搜索响应:', res)
    if (res.msg === 'success') {
      deviceData.value.devices = res.result.rows || []
      deviceData.value.total = res.result.count || 0
    }
    else {
      console.error('搜索设备列表失败:', res.msg)
      deviceData.value.devices = []
      deviceData.value.total = 0
    }
  }).catch(err => {
    console.error('搜索设备列表出错:', err)
    deviceData.value.devices = []
    deviceData.value.total = 0
  }).finally(() => {
    loading.value = false
  })
}

// 刷新设备列表 (保持原有功能)
const refreshDevices = () => {
  loading.value = false

  $get('/v1/device/list', { dType: dType.value, page: page.value, size: itemsPerPage.value }).then(res => {
    console.log(res)
    loading.value = false
    deviceData.value.devices = res.result.rows
    deviceData.value.total = res.result.count
  })
}

const templateForm = reactive({
  mac: '',
  sn: '',
  name: '',
})

const addDevice = () => {
  console.log(templateForm)
  if (currentTab.value === 'batch') {
    closeAddDrawer()
    refreshDevices()
  }
  else {
    $post('/v1/device/bind', templateForm).then(res => {
      console.log(res)
      ElMessage.success('添加设备成功')
      refreshDevices()
      closeAddDrawer()
    }).catch(err => {
      console.error(err)
      ElMessage.error('添加设备失败')
    })
  }
}

const uploader = ref()
const fileList = ref<UploadFile[]>([])

const handleChange = (uploadFile: UploadFile) => {
  fileList.value = [uploadFile]
  console.log(uploadFile, uploadFile.raw)
  if (uploadFile.raw) {
    const formData = new FormData()

    formData.append('file', uploadFile.raw)

    $file('/v1/device/import', formData).then(res => {
      console.log(res)
      ElMessage.success('批量导入设备成功')
      importResults.value = res.result
    }).catch(err => {
      console.error(err)
      ElMessage.error('批量导入设备失败')
    })

    // 保存文件到列表
    fileList.value = [uploadFile]
  }
  else {
    ElMessage.warning('文件格式不正确')
  }
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 监听分页变化，重新搜索
watch([page, itemsPerPage], () => {
  searchDevices()
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  searchDevices()
})
</script>

<template>
  <div class="device-discovery-page">
    <!-- 设备发现列表 -->
    <VCard class="device-table-card">
      <!-- 页面标题和操作按钮 -->
      <div class="d-flex flex-wrap gap-4 ma-6">
        <h2 class="page-title">
          设备发现
        </h2>
        <VSpacer />
        <VBtn
          color="primary"
          variant="tonal"
        >
          导出列表
        </VBtn>
        <VBtn
          color="primary"
          prepend-icon=""
          @click="openAddDrawer"
        >
          +
          添加设备
        </VBtn>
      </div>
      <VDivider />
      <div class="d-flex flex-wrap gap-4 ma-6">
        <VSelect
          v-model="filter.dType"
          :items="typeList"
          class="mr-4"
          item-title="label"
          item-value="value"
        />
        <VSelect
          v-if="shouldShowModelList"
          v-model="filter.model"
          :items="modelList"
          class="mr-4"
          item-title="label"
          item-value="value"
        />

        <VBtn
          color="primary"
          @click="searchDevices"
        >
          搜索
        </VBtn>
      </div>
      <VDataTable
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="devices"
        :loading="loading"
        item-value="id"
        class="text-no-wrap"
        no-data-text="暂无数据"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <!-- 在线状态列 -->
        <template #item.online="{ item }">
          <VChip
            :color="item.online === 'online' ? 'success' : 'error'"
            label
            size="small"
          >
            {{ item.online ? '在线' : '离线' }}
          </VChip>
        </template>
        <!-- 在线状态列 -->
        <template #item.bindTime="{ item }">
          {{ formatTime(item.bindTime) }}
        </template>

        <!-- 底部分页 -->
        <template #bottom>
          <div class="d-flex align-center justify-space-between pa-4">
            <TablePagination
              v-model:page="page"
              :items-per-page="itemsPerPage"
              :total-items="totalDevices"
            />
          </div>
        </template>
      </VDataTable>
    </VCard>

    <!-- 右侧添加设备抽屉 -->
    <VNavigationDrawer
      v-model="isAddDrawerOpen"
      location="end"
      temporary
      width="700"
      class="add-device-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            添加设备
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeAddDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <div class="flex-shrink-0">
                <VTabs v-model="currentTab">
                  <VTab value="manual">
                    手动添加
                  </VTab>
                  <VTab value="batch">
                    批量导入
                  </VTab>
                </VTabs>
              </div>

              <div class="flex-grow-1 overflow-hidden mt-4">
                <VWindow
                  v-model="currentTab"
                  class="h-100 d-flex flex-column"
                >
                  <VWindowItem
                    value="manual"
                    class="flex-grow-1 overflow-auto"
                    style="-ms-overflow-style: none; scrollbar-width: none;"
                  >
                    <VForm class="manual-add-form">
                      <div class="d-flex align-start mb-4">
                        <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                          MAC地址
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-model="templateForm.mac"
                            placeholder="请输入MAC地址"
                            append-inner-icon="tabler-edit"
                          />
                        </div>
                      </div>

                      <div class="d-flex align-start mb-4">
                        <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                          SN
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-model="templateForm.sn"
                            placeholder="请输入序列号"
                            append-inner-icon="tabler-edit"
                          />
                        </div>
                      </div>

                      <div class="d-flex align-start mb-4">
                        <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                          设备名称
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-model="templateForm.name"
                            placeholder="设备名称"
                            append-inner-icon="tabler-edit"
                          />
                        </div>
                      </div>
                    </VForm>
                  </VWindowItem>

                  <VWindowItem
                    value="batch"
                    class="flex-grow-1 overflow-auto"
                    style="-ms-overflow-style: none; scrollbar-width: none;"
                  >
                    <div class="batch-import-form">
                      <div class="d-flex align-start mb-4">
                        <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                          上传文件
                        </div>
                        <div class="w-100">
                          <div class="d-flex align-end">
                            <ElUpload
                              ref="uploader"
                              :auto-upload="false"
                              :limit="1"
                              :multiple="false"
                              :on-change="handleChange"
                              :on-exceed="handleExceed"
                              :show-file-list="false"
                              class="mr-4"
                            >
                              <span class="mr-4 text-secondary">{{
                                fileList && fileList.length ? fileList[0].name : '未选择任何文件'
                              }}</span>
                              <VBtn color="primary">
                                上传Excel
                              </VBtn>
                            </ElUpload>
                          </div>
                        </div>
                      </div>
                      <div class="template-link mt-2">
                        <VIcon
                          icon="mdi-download"
                          size="small"
                          class="download-icon"
                        />
                        <span class="link-text">下载Excel模版</span>
                      </div>
                      <div
                        v-if="importResults.length > 0"
                        class="results-section mt-6"
                      >
                        <div class="results-title mb-4">
                          识别结果
                        </div>
                        <VDataTable
                          v-model:items-per-page="importItemsPerPage"
                          v-model:page="importPage"
                          :headers="importHeaders"
                          :items="importResults"
                          class="import-results-table"
                          :no-data-text="t('NoData')"
                        >
                          <template #item.mac="{ item }">
                            <div>
                              {{ item.mac }}
                            </div>
                            <div>
                              {{ item.sn }}
                            </div>
                          </template>
                          <template #item.errorCode="{ item }">
                            <VChip
                              v-if="item.errorCode === 0"
                              color="success"
                              label
                              size="small"
                            >
                              绑定成功
                            </VChip>
                            <VChip
                              v-if="item.errorCode === 1"
                              color="error"
                              label
                              size="small"
                            >
                              不匹配
                            </VChip>
                            <VChip
                              v-if="item.errorCode === 2"
                              color="error"
                              label
                              size="small"
                            >
                              被绑定
                            </VChip>
                            <VChip
                              v-if="item.errorCode === 3"
                              color="error"
                              label
                              size="small"
                            >
                              不匹配
                            </VChip>
                            <VChip
                              v-if="item.errorCode === 4"
                              color="error"
                              label
                              size="small"
                            >
                              不能为空
                            </VChip>
                          </template>

                          <!-- 添加底部分页 -->
                          <template #bottom>
                            <div class="d-flex align-center justify-space-between pa-4">
                              <div class="text-caption text-disabled">
                                共 {{ importResults.length }} 条
                              </div>
                              <VPagination
                                v-model="importPage"
                                :length="Math.ceil(importResults.length / importItemsPerPage)"
                                :total-visible="5"
                                density="compact"
                              />
                            </div>
                          </template>
                        </VDataTable>
                      </div>
                    </div>
                  </VWindowItem>
                </VWindow>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeAddDrawer"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="addDevice"
            >
              添加设备
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped lang="scss">
.device-discovery-page {
  block-size: 100%;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-block-end: 24px;
}

.device-table-card {
  .table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .pagination-info {
      color: rgba(var(--v-theme-on-surface), 0.6);
      font-size: 14px;
    }

    .pagination-controls {
      margin: 0;
    }
  }
}

.add-device-drawer {
  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-block: 16px;
    padding-inline: 24px;

    .drawer-title {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .drawer-content {
    display: flex;
    flex-direction: column;
    padding: 24px;
    block-size: calc(100% - 73px);

    .drawer-tabs {
      margin-block-end: 24px;
    }

    .tab-content {
      flex: 1;

      .manual-add-form {
        padding-block: 20px;
        padding-inline: 0;

        .form-row {
          display: flex;
          gap: 16px;
          margin-block-end: 20px;

          .form-field {
            &.full-width {
              flex: 1;
            }

            &.half-width {
              flex: 1;
            }
          }
        }
      }

      .batch-import-form {
        padding-block: 20px;
        padding-inline: 0;

        .upload-section {
          margin-block-end: 32px;

          .upload-label {
            color: rgba(var(--v-theme-on-surface), 0.87);
            font-size: 14px;
            font-weight: 500;
            margin-block-end: 12px;
          }

          .upload-area {
            display: flex;
            align-items: flex-end;
            gap: 12px;

            .upload-input {
              flex: 1;
            }

            .upload-btn {
              block-size: 56px;
              min-inline-size: 120px;
            }
          }

          .template-link {
            display: flex;
            align-items: center;
            color: rgb(var(--v-theme-primary));
            cursor: pointer;
            gap: 8px;
            margin-block-start: 16px;

            .download-icon {
              color: rgb(var(--v-theme-primary));
            }

            .link-text {
              font-size: 14px;
              text-decoration: underline;
            }

            &:hover {
              opacity: 0.8;
            }
          }
        }

        .results-section {
          .results-title {
            color: rgba(var(--v-theme-on-surface), 0.87);
            font-size: 16px;
            font-weight: 500;
            margin-block-end: 16px;
          }

          .import-results-table {
            border: 1px solid rgba(var(--v-theme-outline), 0.12);
            border-radius: 4px;

            :deep(.v-data-table__wrapper) {
              border-radius: 4px;
            }
          }
        }
      }
    }

    .drawer-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-block-start: auto;
      padding-block-start: 24px;

      .action-btn {
        min-inline-size: 120px;
      }
    }
  }

  // AP-style form classes
  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }

  .template-link {
    display: flex;
    align-items: center;
    color: rgb(var(--v-theme-primary));
    cursor: pointer;
    gap: 8px;

    .download-icon {
      color: rgb(var(--v-theme-primary));
    }

    .link-text {
      font-size: 14px;
      text-decoration: underline;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .upload-btn {
    block-size: 56px;
    min-inline-size: 120px;
  }
}
</style>
