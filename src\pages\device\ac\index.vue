<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { router } from '@/plugins/1.router'

const { t } = useI18n()

interface ACDevice {
  id: string
  name: string
  model: string
  sn: string
  mac: string
  deviceInfo: {
    wan_ip: ''
    version: ''
  }
  ip: string
  version: string
  online: boolean
}

// 表头配置
const headers = [
  { title: '名称', key: 'name', sortable: true },
  { title: '型号', key: 'model', sortable: true },
  { title: 'SN', key: 'sn', sortable: true },
  { title: 'IP', key: 'ip', sortable: true },
  { title: 'MAC', key: 'mac', sortable: true },
  { title: '固件版本', key: 'version', sortable: true },
  { title: '运行时间', key: 'runTime', sortable: true },
  { title: '在线状态', key: 'online', sortable: false },
  { title: '操作', key: 'actions', sortable: false },
]

// 响应式数据
const loading = ref(false)

const deviceData = ref({
  total: 0,
  devices: [] as ACDevice[],
})

const selectedDevices = ref<string[]>([])
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref<{ key: string; order?: 'asc' | 'desc' }[]>([])

// 抽屉状态管理
const isAddDrawerOpen = ref(false)
const isManageDrawerOpen = ref(false)
const currentTab = ref('manual')
const currentDevice = ref<ACDevice | null>(null)

// 返回设备数据
const devices = computed((): ACDevice[] => {
  return deviceData.value.devices
})

// 计算总数
const totalDevices = computed(() => deviceData.value.total)

// 获取AC设备列表 - 参考设备添加页面实现
const getACDeviceList = () => {
  loading.value = true

  const params = {
    dType: 1, // AC设备类型
    page: page.value,
    size: itemsPerPage.value,
  }

  $get('/v1/device/list', params).then(res => {
    console.log('AC设备列表响应:', res)
    if (res.msg === 'success') {
      deviceData.value.total = res.result.count || 0

      // 处理设备数据，添加必要的字段映射
      deviceData.value.devices = (res.result.rows || [])
        .filter((item: any) => item.sn) // 过滤掉没有SN的设备
        .map((item: any) => ({
          id: item.id, // 使用SN作为ID
          name: item.user_name || item.name || '--',
          model: item.model || '--',
          sn: item.sn,
          ip: item.deviceInfo?.wan_ip || item.ip || '--',
          mac: item.mac || '--',
          version: item.deviceInfo?.version || '--',
          runTime: item.deviceInfo?.runTime || '--',
          online: item.online,
        }))
    }
    else {
      console.error('获取AC设备列表失败:', res.msg)
      deviceData.value.devices = []
      deviceData.value.total = 0
    }
  }).catch(err => {
    console.error('获取AC设备列表出错:', err)
    deviceData.value.devices = []
    deviceData.value.total = 0
  }).finally(() => {
    loading.value = false
  })
}

// 排序事件
const sortchange = (val: { key: string; order?: 'asc' | 'desc' }[]) => {
  sortBy.value = val
}

// 打开添加设备抽屉
const openAddDrawer = () => {
  // isAddDrawerOpen.value = true
  // currentTab.value = 'manual'
  router.push('/system/networkConfig')
}

// 关闭添加设备抽屉
const closeAddDrawer = () => {
  isAddDrawerOpen.value = false
}

// 打开设备管理抽屉
const openManageDrawer = (device: ACDevice) => {
  // currentDevice.value = device
  // isManageDrawerOpen.value = true
  router.push('/system/systemConfig')
}

// 关闭设备管理抽屉
const closeManageDrawer = () => {
  isManageDrawerOpen.value = false
  currentDevice.value = null
}

const handleAction = (action: string, deviceId: string) => {
  const device = devices.value.find(d => d.id === deviceId)

  switch (action) {
  case '设备管理':
    if (device)
      openManageDrawer(device)
    break
  case '重启设备':
    console.log('重启设备', deviceId)
    restartDevice(deviceId)
    break
  case '删除设备':
    console.log('删除设备', deviceId)
      deleteDevice(deviceId)
    break
  default:
    console.log(`${action} for device ${deviceId}`)
  }
}

const restartDevice = (deviceId: string) => {
  const device = devices.value.find(d => d.id === deviceId)
  if (!device) {
    ElMessage.error('设备不存在')

    return
  }

  $post('/v1/bulkCmd', {
    deviceSns: [device.sn],
    cmd: 'restart',
  }).then(res => {
    if (res.msg === 'success')
      getAPStatus(res.result)
    ElMessage.success('重启AC命令已发送')
  })
}

const deleteDevice = (deviceId: string) => {
  const device = devices.value.find(d => d.id === deviceId)

  console.log(device, deviceId, devices.value)
  if (!device) {
    ElMessage.error('设备不存在')

    return
  }

  ElMessageBox.confirm('确定要删除该设备吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    $delete(`/v1/device/${device.id}`, {}).then(res => {
      if (res.msg === 'success') {
        ElMessage.success('删除成功')
        getACDeviceList()
      }
      else {
        ElMessage.error(res.msg || '删除失败')
      }
    })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const getAPStatus = (bulkId: string) => {
  setTimeout(() => {
    $get(`/v1/bulkCmdResult/${bulkId}`, {}).then(res => {
      const rows = res.result
      if (rows[0].result == true)
        ElMessage.success('执行成功')
      else
        ElMessage.error('执行失败')
    }).catch(error => {
      console.error('获取设备状态失败:', error)
      ElMessage.error('获取设备状态失败')
    })
  }, 3000)
}

const addDevice = () => {
  openAddDrawer()
}

// 添加分页参数监听 - 参考设备添加页面实现
watch([page, itemsPerPage], () => {
  getACDeviceList()
}, { immediate: false })

// 组件挂载时获取数据
onMounted(() => {
  getACDeviceList()
})
</script>

<template>
  <div class="ac-management-page">
    <!-- AC管理列表 -->
    <VCard class="device-table-card">
      <!-- 页面标题和操作按钮 -->
      <div class="d-flex flex-wrap gap-4 ma-6">
        <h2 class="page-title">
          AC管理
        </h2>
        <VSpacer />
      </div>
      <VDivider />
      <VDataTableServer
        v-model="selectedDevices"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="devices"
        :items-length="totalDevices"
        :loading="loading"
        show-select
        item-value="id"
        class="text-no-wrap"
        :no-data-text="t('NoData')"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <!-- IP列 -->
        <template #item.ip="{ item }">
          <a
            href="#"
            class="ip-link"
          >{{ item.ip || '--' }}</a>
        </template>
        <template #item.version="{ item }">
          {{ item.version || '--' }}
        </template>
        <!-- 在线状态列 -->
        <template #item.online="{ item }">
          <VChip
            :color="item.online ? 'success' : 'error'"
            label
            size="small"
          >
            {{ item.online ? '在线' : '离线' }}
          </VChip>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <VMenu>
            <template #activator="{ props }">
              <VBtn
                icon
                size="small"
                variant="text"
                v-bind="props"
              >
                <VIcon icon="tabler-dots-vertical" />
              </VBtn>
            </template>
            <VList>
              <VListItem @click="handleAction('设备管理', item.id)">
                <VListItemTitle>设备管理</VListItemTitle>
              </VListItem>
              <VListItem @click="handleAction('重启设备', item.id)">
                <VListItemTitle>重启设备</VListItemTitle>
              </VListItem>
              <VListItem @click="handleAction('删除设备', item.id)">
                <VListItemTitle class="text-error">
                  删除设备
                </VListItemTitle>
              </VListItem>
            </VList>
          </VMenu>
        </template>

        <!-- 底部分页 -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalDevices"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <!-- 右侧添加设备抽屉 -->
    <VNavigationDrawer
      v-model="isAddDrawerOpen"
      location="end"
      temporary
      width="600"
      class="add-device-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            添加AC设备
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeAddDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4">
              <!-- 内容区域留空，但保持滚动功能 -->
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeAddDrawer"
            >
              取消
            </VBtn>
            <VBtn color="primary">
              添加设备
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 右侧设备管理抽屉 -->
    <VNavigationDrawer
      v-model="isManageDrawerOpen"
      location="end"
      temporary
      width="600"
      class="manage-device-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            设备管理 - {{ currentDevice?.name }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeManageDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4">
              <!-- 内容区域留空，但保持滚动功能 -->
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div>
            <VBtn
              color="error"
              variant="outlined"
            >
              删除设备
            </VBtn>
          </div>
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeManageDrawer"
            >
              取消
            </VBtn>
            <VBtn color="primary">
              保存更改
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped lang="scss">
.ac-management-page {
  block-size: 100%;
}

.device-table-card {
  .ip-link {
    color: rgb(var(--v-theme-primary));
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.add-device-drawer,
.manage-device-drawer {
  // AP-style form classes
  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }

  .template-link {
    display: flex;
    align-items: center;
    color: rgb(var(--v-theme-primary));
    cursor: pointer;
    gap: 8px;

    .download-icon {
      color: rgb(var(--v-theme-primary));
    }

    .link-text {
      font-size: 14px;
      text-decoration: underline;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
