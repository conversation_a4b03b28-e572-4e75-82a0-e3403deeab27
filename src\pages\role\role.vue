<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { $delete, $get, $post, $put } from '../../utils/api' // 请根据实际路径调整

// 表头
const headers = [
  { title: '角色名称', key: 'name', sortable: false, width: '120px' },
  { title: '描述', key: 'description', sortable: false, width: '150px' },
  { title: '权限配置', key: 'permissions', sortable: false, style: 'white-space: normal; word-break: break-all;' },
  { title: '创建时间', key: 'createdAt', sortable: false, width: '180px' },
  { title: '操作', key: 'actions', sortable: false, width: '80px' },
]

// 权限项类型定义
interface PermissionItem {
  category: string
  module: string
  action: string
  code: string
}

// 角色接口定义
interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  createdAt: string
  type?: string
  createdById?: string | null
}

// 基于截图中的权限结构定义权限数据
const permissions: PermissionItem[] = [
  { category: '系统管理', module: '无线调优', action: 'read', code: 'sys:wan:read' },
  { category: '系统管理', module: '查看成员', action: 'read', code: 'sys:member:read' },
  { category: '系统管理', module: '查看角色', action: 'read', code: 'sys:role:read' },
  { category: '设备管理', module: 'AP管理', action: 'read', code: 'dev:ap:read' },
  { category: '设备管理', module: 'AP分组', action: 'read', code: 'dev:ap-group:read' },
  { category: '设备管理', module: 'AC管理', action: 'read', code: 'dev:ac:read' },
  { category: '配置管理', module: 'VLAN配置', action: 'read', code: 'conf:vlan:read' },
  { category: '配置管理', module: '模板管理', action: 'read', code: 'conf:template:read' },
  { category: '配置管理', module: 'AP配置下发', action: 'read', code: 'conf:ap-emit:read' },
  { category: '网络管理', module: '网络拓扑', action: 'read', code: 'network:topology:read' },
  { category: '网络管理', module: 'AP数据', action: 'read', code: 'network:ap:read' },
  { category: '网络管理', module: '终端数据', action: 'read', code: 'network:terminal:read' },
  { category: '网络管理', module: '网络运维', action: 'read', code: 'network:ops:read' },
  { category: '网络管理', module: '网络事件', action: 'read', code: 'network:event:read' },
  { category: '网络管理', module: '网络状态', action: 'read', code: 'network:status-net:read' },
  { category: '网络管理', module: '设备状态', action: 'read', code: 'network:status-device:read' },
  { category: '网络管理', module: '终端状态', action: 'read', code: 'network:status-terminal:read' },

  { category: '系统管理', module: '无线调优', action: 'manage', code: 'sys:wan:manage' },
  { category: '系统管理', module: '添加成员', action: 'create', code: 'sys:member:create' },
  { category: '系统管理', module: '编辑成员', action: 'update', code: 'sys:member:update' },
  { category: '系统管理', module: '删除成员', action: 'delete', code: 'sys:member:delete' },
  { category: '系统管理', module: '导出成员', action: 'export', code: 'sys:member:export' },
  { category: '系统管理', module: '添加角色', action: 'create', code: 'sys:role:create' },
  { category: '系统管理', module: '编辑角色', action: 'update', code: 'sys:role:update' },
  { category: '系统管理', module: '删除角色', action: 'delete', code: 'sys:role:delete' },
  { category: '系统管理', module: '导出角色', action: 'export', code: 'sys:role:export' },
  { category: '设备管理', module: 'AP管理', action: 'manage', code: 'dev:ap:manage' },
  { category: '设备管理', module: '设备添加', action: 'manage', code: 'dev:device-create:manage' },
  { category: '设备管理', module: 'AP分组', action: 'manage', code: 'dev:ap-group:manage' },
  { category: '设备管理', module: 'AC管理', action: 'manage', code: 'dev:ac:manage' },
  { category: '配置管理', module: 'VLAN配置', action: 'manage', code: 'conf:vlan:manage' },
  { category: '配置管理', module: '模板管理', action: 'manage', code: 'conf:template:manage' },
  { category: '配置管理', module: 'AP配置下发', action: 'manage', code: 'conf:ap-emit:manage' },
  { category: '网络管理', module: '网络事件', action: 'manage', code: 'network:event:manage' },
  { category: '网络管理', module: '客户端管理', action: 'manage', code: 'network:client:manage' },
]

// 分离查看权限和编辑权限
const readPermissions = computed(() => {
  return permissions.filter(p => p.action === 'read')
})

const writePermissions = computed(() => {
  return permissions.filter(p => p.action !== 'read')
})

// 按分类组织权限
const getPermissionsByCategory = (permissionsList: PermissionItem[]) => {
  const categories = [...new Set(permissionsList.map(p => p.category))]

  return categories.map(category => {
    const categoryPerms = permissionsList.filter(p => p.category === category)

    return {
      category,
      permissions: categoryPerms,
    }
  })
}

// 按类别组织的查看和编辑权限
const readPermissionsByCategory = computed(() => getPermissionsByCategory(readPermissions.value))
const writePermissionsByCategory = computed(() => getPermissionsByCategory(writePermissions.value))

// 获取模块名称显示
const getModuleNamesByPermissionCodes = (permissionCodes: string[]) => {
  if (!permissionCodes || !Array.isArray(permissionCodes))
    return []

  const moduleNames: any = []

  permissionCodes.forEach(permCode => {
    const perm = permissions.find(p => p.code === permCode)
    if (perm) {
      const obj = JSON.parse(JSON.stringify(perm))

      obj.color = permCode.includes(':read') ? 'primary' : 'success'
      console.log(obj.color, permCode)
      moduleNames.push(obj)
    }
  })

  // 将数组拆分为两组，一组是color为primary的，一组是color为success的
  const readPermissions = moduleNames.filter((item: any) => item.color === 'primary')
  const writePermissions = moduleNames.filter((item: any) => item.color === 'success')

  console.log(readPermissions, writePermissions)

  // 只返回有数据的分类
  const result = []

  if (readPermissions.length > 0) {
    result.push({
      category: '查看权限',
      permissions: readPermissions,
    })
  }

  if (writePermissions.length > 0) {
    result.push({
      category: '编辑权限',
      permissions: writePermissions,
    })
  }

  return result
}

// 示例数据，会被API数据覆盖
const roles = ref<Role[]>([])

const search = ref('')
const page = ref(1)
const itemsPerPage = ref(10)

const drawerVisible = ref(false)
const isEdit = ref(false)

const editRole = ref<Role>({
  id: '',
  name: '',
  description: '',
  permissions: [],
  createdAt: new Date().toISOString(),
})

// 权限全选状态
const allReadSelected = ref(false)
const allWriteSelected = ref(false)

// 权限半选状态
const allReadIndeterminate = ref(false)
const allWriteIndeterminate = ref(false)

// 防止循环触发的标志
const isUpdatingPermissions = ref(false)

// 标记是否是用户主动点击全选框
const isUserClickingSelectAll = ref(false)

// 添加用于管理分类半选中状态的变量
const categoryReadIndeterminate = ref<Record<string, boolean>>({})
const categoryWriteIndeterminate = ref<Record<string, boolean>>({})

// 监听全选状态变化 - 只响应用户主动点击
watch(allReadSelected, (newVal, oldVal) => {
  // 只有当值真正改变时才处理，避免初始化时的触发
  if (newVal === oldVal || isUpdatingPermissions.value)
    return

  // 只有用户主动点击全选框时才执行全选/取消全选操作
  if (!isUserClickingSelectAll.value)
    return

  isUpdatingPermissions.value = true

  if (newVal) {
    // 添加所有查看权限
    const readCodes = readPermissions.value.map(p => p.code)

    readCodes.forEach(code => {
      if (!editRole.value.permissions.includes(code))
        editRole.value.permissions.push(code)
    })

    // 选中全部时清除半选状态
    allReadIndeterminate.value = false
  }
  else {
    // 移除所有查看权限
    editRole.value.permissions = editRole.value.permissions.filter(
      code => !readPermissions.value.map(p => p.code).includes(code),
    )

    // 取消全选时也清除半选状态
    allReadIndeterminate.value = false
  }

  // 重置所有分类的半选中状态
  updateAllCategoryIndeterminateStates()

  isUpdatingPermissions.value = false
  isUserClickingSelectAll.value = false // 重置标志
})

watch(allWriteSelected, (newVal, oldVal) => {
  // 只有当值真正改变时才处理，避免初始化时的触发
  if (newVal === oldVal || isUpdatingPermissions.value)
    return

  // 只有用户主动点击全选框时才执行全选/取消全选操作
  if (!isUserClickingSelectAll.value)
    return

  isUpdatingPermissions.value = true

  if (newVal) {
    // 添加所有编辑权限
    const writeCodes = writePermissions.value.map(p => p.code)

    writeCodes.forEach(code => {
      if (!editRole.value.permissions.includes(code))
        editRole.value.permissions.push(code)
    })

    // 选中全部时清除半选状态
    allWriteIndeterminate.value = false
  }
  else {
    // 移除所有编辑权限
    editRole.value.permissions = editRole.value.permissions.filter(
      code => !writePermissions.value.map(p => p.code).includes(code),
    )

    // 取消全选时也清除半选状态
    allWriteIndeterminate.value = false
  }

  // 重置所有分类的半选中状态
  updateAllCategoryIndeterminateStates()

  isUpdatingPermissions.value = false
  isUserClickingSelectAll.value = false // 重置标志
})

// 监听权限变化，更新全选状态和半选中状态
watch(() => editRole.value.permissions, newVal => {
  if (!newVal) {
    editRole.value.permissions = [] // 确保permissions永远不为null

    return
  }

  // 如果正在更新权限，跳过此次监听
  if (isUpdatingPermissions.value)
    return

  // 检查查看权限是否全选
  const readCodes = readPermissions.value.map(p => p.code)
  const readSelectedCount = readCodes.filter(code => newVal.includes(code)).length

  // 更新查看权限状态（不触发全选操作）
  allReadSelected.value = readSelectedCount === readCodes.length
  allReadIndeterminate.value = readSelectedCount > 0 && readSelectedCount < readCodes.length

  // 检查编辑权限是否全选
  const writeCodes = writePermissions.value.map(p => p.code)
  const writeSelectedCount = writeCodes.filter(code => newVal.includes(code)).length

  // 更新编辑权限状态（不触发全选操作）
  allWriteSelected.value = writeSelectedCount === writeCodes.length
  allWriteIndeterminate.value = writeSelectedCount > 0 && writeSelectedCount < writeCodes.length

  // 更新所有分类的半选中状态
  updateAllCategoryIndeterminateStates()
}, { deep: true })

// 更新所有分类的半选中状态
function updateAllCategoryIndeterminateStates() {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  // 更新查看权限的半选中状态
  readPermissionsByCategory.value.forEach(category => {
    updateCategoryIndeterminateState(category.category, 'read')
  })

  // 更新编辑权限的半选中状态
  writePermissionsByCategory.value.forEach(category => {
    updateCategoryIndeterminateState(category.category, 'write')
  })
}

// 更新单个分类的半选中状态
function updateCategoryIndeterminateState(category: string, permType: 'read' | 'write') {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  const permissions = permType === 'read' ? readPermissions.value : writePermissions.value
  const categoryCodes = permissions.filter(p => p.category === category).map(p => p.code)

  // 计算该分类下选中的权限数量
  const selectedCount = categoryCodes.filter(code =>
    editRole.value.permissions.includes(code),
  ).length

  // 如果部分选中但不是全部，设置为半选中状态
  if (permType === 'read')
    categoryReadIndeterminate.value[category] = selectedCount > 0 && selectedCount < categoryCodes.length
  else
    categoryWriteIndeterminate.value[category] = selectedCount > 0 && selectedCount < categoryCodes.length
}

// 分页和搜索
const filteredRoles = computed(() => {
  let list = roles.value
  if (search.value)
    list = list.filter(r => r.name.includes(search.value))

  return list
})

const pagedRoles = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value

  return filteredRoles.value.slice(start, start + itemsPerPage.value)
})

// 专门用于创建角色的函数
function createNewRole() {
  isEdit.value = false
  editRole.value = {
    id: '',
    name: '',
    description: '',
    permissions: [],
    createdAt: new Date().toISOString(),
  }

  // 初始化全选状态
  allReadSelected.value = false
  allWriteSelected.value = false

  // 初始化半选中状态
  allReadIndeterminate.value = false
  allWriteIndeterminate.value = false

  // 初始化所有分类的半选中状态
  categoryReadIndeterminate.value = {}
  categoryWriteIndeterminate.value = {}

  // 打开抽屉
  drawerVisible.value = true
}

// 更新权限选择的状态
function updatePermissionSelectionStates() {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  // 检查查看权限状态
  const readCodes = readPermissions.value.map(p => p.code)

  const readSelectedCount = readCodes.filter(code =>
    editRole.value.permissions.includes(code),
  ).length

  // 更新查看权限状态
  allReadSelected.value = readSelectedCount === readCodes.length
  allReadIndeterminate.value = readSelectedCount > 0 && readSelectedCount < readCodes.length

  // 检查编辑权限状态
  const writeCodes = writePermissions.value.map(p => p.code)

  const writeSelectedCount = writeCodes.filter(code =>
    editRole.value.permissions.includes(code),
  ).length

  // 更新编辑权限状态
  allWriteSelected.value = writeSelectedCount === writeCodes.length
  allWriteIndeterminate.value = writeSelectedCount > 0 && writeSelectedCount < writeCodes.length

  // 更新所有分类的半选中状态
  updateAllCategoryIndeterminateStates()
}

// 添加处理单个权限选择变化的方法
function handlePermissionChange(code: string, isChecked: boolean) {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  if (isChecked) {
    // 如果选中，添加到权限列表
    if (!editRole.value.permissions.includes(code))
      editRole.value.permissions.push(code)
  }
  else {
    // 如果取消选中，从权限列表中移除
    editRole.value.permissions = editRole.value.permissions.filter(c => c !== code)
  }

  // 更新权限选择状态
  updatePermissionSelectionStates()
}

// 确保在所有使用permissions的地方都不会为null
function ensurePermissionsArray() {
  if (!editRole.value.permissions)
    editRole.value.permissions = []
}

// 检查单个权限是否被选中
function isPermissionSelected(code: string): boolean {
  ensurePermissionsArray()

  return editRole.value.permissions.includes(code)
}

// 打开侧滑
function openDrawer(role?: Role) {
  if (role) {
    isEdit.value = true

    // 使用深拷贝避免引用问题
    editRole.value = JSON.parse(JSON.stringify(role))
  }
  else {
    isEdit.value = false

    // 重置为默认值
    editRole.value = {
      id: '',
      name: '',
      description: '',
      permissions: [],
      createdAt: new Date().toISOString(),
    }
  }

  // 确保permissions存在
  ensurePermissionsArray()

  // 初始化全选和半选状态
  updatePermissionSelectionStates()

  // 确保开启侧边栏
  drawerVisible.value = true
}

// 保存角色
async function saveRole() {
  if (!editRole.value.name) {
    ElMessage.error('请输入角色名称')

    return
  }

  let success = false

  if (isEdit.value)
    success = await updateRole(editRole.value)
  else
    success = await createRole(editRole.value)

  if (success)
    drawerVisible.value = false
}

// 删除角色
function deleteRole(role: Role) {
  ElMessageBox.confirm(`确定删除角色 ${role.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    deleteRoleById(role.id)
  }).catch(() => {})
}

// 复制角色
function copyRole(role: Role) {
  const newRole: Partial<Role> = {
    name: `${role.name}-复制`,
    description: role.description,
    permissions: [...role.permissions],
  }

  createRole(newRole as Role)
}

// 切换某个分类的所有查看权限
function toggleCategoryReadPermissions(category: string, checked: boolean) {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  const categoryCodes = readPermissions.value
    .filter(p => p.category === category)
    .map(p => p.code)

  if (checked) {
    // 添加当前没有的权限
    categoryCodes.forEach(code => {
      if (!editRole.value.permissions.includes(code))
        editRole.value.permissions.push(code)
    })
  }
  else {
    // 移除该分类的所有查看权限
    editRole.value.permissions = editRole.value.permissions.filter(
      code => !categoryCodes.includes(code),
    )
  }

  // 更新该分类的半选中状态
  updateCategoryIndeterminateState(category, 'read')
}

// 切换某个分类的所有编辑权限
function toggleCategoryWritePermissions(category: string, checked: boolean) {
  // 确保permissions存在
  if (!editRole.value.permissions)
    editRole.value.permissions = []

  const categoryCodes = writePermissions.value
    .filter(p => p.category === category)
    .map(p => p.code)

  if (checked) {
    // 添加当前没有的权限
    categoryCodes.forEach(code => {
      if (!editRole.value.permissions.includes(code))
        editRole.value.permissions.push(code)
    })
  }
  else {
    // 移除该分类的所有编辑权限
    editRole.value.permissions = editRole.value.permissions.filter(
      code => !categoryCodes.includes(code),
    )
  }

  // 更新该分类的半选中状态
  updateCategoryIndeterminateState(category, 'write')
}

// 检查分类查看权限是否全部选中
function isCategoryReadAllSelected(category: string) {
  const categoryCodes = readPermissions.value
    .filter(p => p.category === category)
    .map(p => p.code)

  return categoryCodes.every(code => editRole.value.permissions && editRole.value.permissions.includes(code))
}

// 检查分类编辑权限是否全部选中
function isCategoryWriteAllSelected(category: string) {
  const categoryCodes = writePermissions.value
    .filter(p => p.category === category)
    .map(p => p.code)

  return categoryCodes.every(code => editRole.value.permissions && editRole.value.permissions.includes(code))
}

// 检查分类查看权限是否有任意一个选中（用于半选中状态）
function isCategoryReadIndeterminate(category: string) {
  return categoryReadIndeterminate.value[category] || false
}

// 检查分类编辑权限是否有任意一个选中（用于半选中状态）
function isCategoryWriteIndeterminate(category: string) {
  return categoryWriteIndeterminate.value[category] || false
}

// 获取角色列表
async function getRoleList() {
  try {
    const res = await $get('/v1/roles', {
      page: 1,
      size: 1000,
    })

    console.log(res)
    if (res.msg === 'success')
      roles.value = res.result.rows
  }
  catch (error) {
    console.error('获取角色列表失败', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 创建角色
async function createRole(role: Role) {
  try {
    const res = await $post('/v1/role', {
      name: role.name,
      description: role.description,
      permissions: role.permissions,
    })

    if (res.msg === 'success') {
      ElMessage.success('创建成功')
      getRoleList()

      return true
    }
    else {
      ElMessage.error(res.msg || '创建失败')

      return false
    }
  }
  catch (error) {
    console.error('创建角色失败', error)
    ElMessage.error('创建角色失败')

    return false
  }
}

// 更新角色
async function updateRole(role: Role) {
  try {
    const res = await $put(`/v1/role/${role.id}`, {
      name: role.name,
      description: role.description,
      permissions: role.permissions,
    })

    if (res.msg === 'success') {
      ElMessage.success('更新成功')
      getRoleList()

      return true
    }
    else {
      ElMessage.error(res.msg || '更新失败')

      return false
    }
  }
  catch (error) {
    console.error('更新角色失败', error)
    ElMessage.error('更新角色失败')

    return false
  }
}

// 删除角色
async function deleteRoleById(id: string) {
  try {
    const res = await $delete(`/v1/role/${id}`)

    if (res.msg === 'success') {
      ElMessage.success('删除成功')
      getRoleList()

      return true
    }
    else {
      ElMessage.error(res.msg || '删除失败')

      return false
    }
  }
  catch (error) {
    console.error('删除角色失败', error)
    ElMessage.error('删除角色失败')

    return false
  }
}

// 初始化
onMounted(() => {
  getRoleList()
})
</script>

<template>
  <div>
    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center gap-4">
            <div>角色管理</div>
            <AppTextField
              v-model="search"
              placeholder="搜索角色"
              density="compact"
              hide-details
              style="min-inline-size: 150px;"
            />
          </div>
          <div class="d-flex gap-4">
            <VBtn
              color="primary"
              @click="createNewRole"
            >
              + 创建角色
            </VBtn>
          </div>
        </div>
      </template>
      <VDivider />

      <!-- 角色表格 -->
      <VDataTable
        :headers="headers"
        :items="pagedRoles"
        item-value="id"
        class="role-table"
        fixed-header
      >
        <template #item.name="{ item }">
          <div class="nowrap-cell d-flex align-center">
            <span>{{ item.name }}</span>
          </div>
        </template>
        <template #item.description="{ item }">
          <div class="nowrap-cell">
            {{ item.description || '暂无描述' }}
          </div>
        </template>
        <template #item.type="{ item }">
          <VChip
            :color="item.type === 'SYSTEM' ? 'warning' : 'primary'"
            size="small"
            variant="tonal"
          >
            {{ item.type === 'SYSTEM' ? '系统角色' : '自定义角色' }}
          </VChip>
        </template>
        <template #item.createdAt="{ item }">
          <div class="nowrap-cell">
            {{ new Date(item.createdAt).toLocaleString() }}
          </div>
        </template>
        <template #item.permissions="{ item }">
          <div class="permissions-container mt-2">
            <div
              v-for="list in getModuleNamesByPermissionCodes(item.permissions)"
              :key="list.category"
              class="mb-2"
            >
              <div class="d-flex flex-wrap align-center">
                <div class="fs-14 mr-2 mb-1 font-weight-medium">
                  {{ list.category }}:
                </div>
                <VChip
                  v-for="moduleName in list.permissions"
                  :key="moduleName.code"
                  size="small"
                  class="mr-1 mb-1"
                  :color="moduleName.color"
                >
                  {{ moduleName.module }}
                </VChip>
              </div>
            </div>
          </div>
        </template>
        <template #item.actions="{ item }">
          <!-- 系统角色不显示任何操作 -->
          <div
            v-if="item.type === 'SYSTEM'"
            class="text-center text-disabled"
          >
            --
          </div>
          <!-- 自定义角色显示操作菜单 -->
          <VMenu
            v-else
            location="bottom end"
          >
            <template #activator="{ props }">
              <VBtn
                icon
                variant="text"
                color="default"
                size="small"
                v-bind="props"
              >
                <VIcon icon="tabler-dots-vertical" />
              </VBtn>
            </template>
            <VList>
              <VListItem @click="openDrawer(item)">
                编辑
              </VListItem>
              <VListItem @click="deleteRole(item)">
                删除
              </VListItem>
              <VListItem @click="copyRole(item)">
                复制
              </VListItem>
            </VList>
          </VMenu>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="filteredRoles.length"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- 添加/编辑角色弹窗 -->
    <VNavigationDrawer
      v-model="drawerVisible"
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ isEdit ? '编辑角色' : '添加角色' }}
          </div>
          <VBtn
            icon
            variant="text"
            @click="drawerVisible = false"
          >
            <VIcon
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VForm @submit.prevent="saveRole">
              <div class="mb-6 pt-4">
                <VTextField
                  v-model="editRole.name"
                  label="角色名称"
                  placeholder="请输入角色名称"
                  variant="outlined"
                  dense
                />
              </div>
              <div class="mb-6">
                <VTextField
                  v-model="editRole.description"
                  label="描述"
                  placeholder="请输入角色描述"
                  variant="outlined"
                  dense
                />
              </div>

              <div class="text-h6 mb-4">
                权限分配
              </div>
              <div class="borderBox">
                <!-- 查看权限部分 -->
                <div class="mb-6">
                  <div class="d-flex align-center mb-4">
                    <VCheckbox
                      v-model="allReadSelected"
                      :indeterminate="allReadIndeterminate"
                      label="查看权限"
                      hide-details
                      color="primary"
                      @click="isUserClickingSelectAll = true"
                    />
                  </div>

                  <div
                    v-for="category in readPermissionsByCategory"
                    :key="`read-${category.category}`"
                    class="mb-4 pt-2"
                    style="background-color: rgba(250, 250, 250, 100%);"
                  >
                    <div class="d-flex align-center mb-2">
                      <VCheckbox
                        v-if="false"
                        :model-value="isCategoryReadAllSelected(category.category)"
                        :indeterminate="isCategoryReadIndeterminate(category.category)"
                        hide-details
                        color="primary"
                        @update:model-value="toggleCategoryReadPermissions(category.category, $event)"
                      />
                      <span
                        class="text-subtitle-1"
                        :style="{ marginLeft: '32px' }"
                      >{{ category.category }}</span>
                    </div>

                    <div class="d-flex flex-wrap permission-group">
                      <div
                        v-for="perm in category.permissions"
                        :key="perm.code"
                        class="permission-item"
                      >
                        <VCheckbox
                          v-model="editRole.permissions"
                          :value="perm.code"
                          :label="perm.module"
                          hide-details
                          density="compact"
                          color="primary"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 编辑权限部分 -->
                <div class="mb-6 editBox">
                  <div class="d-flex align-center mb-4">
                    <VCheckbox
                      v-model="allWriteSelected"
                      :indeterminate="allWriteIndeterminate"
                      label="编辑权限"
                      hide-details
                      color="success"
                      class="font-weight-bold"
                      @click="isUserClickingSelectAll = true"
                    />
                  </div>

                  <div
                    v-for="category in writePermissionsByCategory"
                    :key="`write-${category.category}`"
                    class="mb-4 pt-2"
                    style="background-color: rgba(250, 250, 250, 100%);"
                  >
                    <div class="d-flex align-center mb-2">
                      <VCheckbox
                        v-if="false"
                        :model-value="isCategoryWriteAllSelected(category.category)"
                        :indeterminate="isCategoryWriteIndeterminate(category.category)"
                        hide-details
                        color="success"
                        class="write-permission-checkbox"
                        @update:model-value="toggleCategoryWritePermissions(category.category, $event)"
                      />
                      <span
                        class="text-subtitle-1"
                        :style="{ marginLeft: '32px' }"
                      >{{ category.category }}</span>
                    </div>

                    <div class="d-flex flex-wrap permission-group">
                      <div
                        v-for="perm in category.permissions"
                        :key="perm.code"
                        class="permission-item"
                      >
                        <VCheckbox
                          v-model="editRole.permissions"
                          :value="perm.code"
                          :label="perm.module"
                          hide-details
                          density="compact"
                          color="success"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </VForm>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="flex-shrink-0">
          <VDivider />
          <div class="pa-4 d-flex justify-end">
            <VBtn
              variant="tonal"
              color="secondary"
              class="mr-3"
              @click="drawerVisible = false"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveRole"
            >
              保存
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped>
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.role-table td {
  padding-block: 12px;
  vertical-align: top;
  white-space: nowrap;
}

.role-table td:nth-child(3) {
  white-space: normal !important;
  word-break: break-all;
}

.nowrap-cell {
  overflow: hidden;
  max-inline-size: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.permissions-container {
  min-inline-size: 300px;
  white-space: normal;
  word-break: break-all;
}

.permission-group {
  margin-inline-start: 32px;
}

.permission-item {
  margin-block-end: 8px;
  margin-inline-end: 16px;
}

.fs-14 {
  font-size: 14px;
}

/* 解决半选中绿色问题 */
.editBox :deep(.v-checkbox:not(.v-selection-control--dirty) .v-selection-control__input > .custom-checkbox-indeterminate, .v-checkbox-btn:not(.v-selection-control--dirty) .v-selection-control__input > .custom-checkbox-indeterminate) {
  color: rgb(var(--v-theme-success)) !important;
}

.borderBox {
  padding: 12px;
  border: 1px solid rgba(47, 43, 61, 22%);
  border-radius: 6px;
}
</style>
